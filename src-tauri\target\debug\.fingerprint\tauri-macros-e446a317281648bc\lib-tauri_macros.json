{"rustc": 16591470773350601817, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 5644833025087993307, "deps": [[2671782512663819132, "tauri_utils", false, 3599876494198880509], [3060637413840920116, "proc_macro2", false, 2047289033467063536], [4974441333307933176, "syn", false, 1189136657649781392], [13077543566650298139, "heck", false, 3525024826125644909], [14455244907590647360, "tauri_codegen", false, 14931452511247300419], [17990358020177143287, "quote", false, 13298145250187227537]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-e446a317281648bc\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}