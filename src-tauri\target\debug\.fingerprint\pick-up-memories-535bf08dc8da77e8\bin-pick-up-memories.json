{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[]", "target": 6269644749152266277, "profile": 17672942494452627365, "path": 4942398508502643691, "deps": [[6416823254013318197, "tauri_plugin_fs", false, 7706622683557411794], [7083681497971801814, "pick_up_memories_lib", false, 16364574842403965282], [7083681497971801814, "build_script_build", false, 11142410979891787431], [7760050409050412348, "tauri_plugin_notification", false, 9975918143436112709], [9689903380558560274, "serde", false, 11684644914909789052], [9897246384292347999, "chrono", false, 15346877739613643899], [13077212702700853852, "base64", false, 18299951285725630176], [14039947826026167952, "tauri", false, 13489916834868122564], [14525517306681678134, "tauri_plugin_dialog", false, 16499904188771389839], [15367738274754116744, "serde_json", false, 453405134307750339], [16702348383442838006, "tauri_plugin_opener", false, 4387712998567406808]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\pick-up-memories-535bf08dc8da77e8\\dep-bin-pick-up-memories", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}