{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"win7-notifications\", \"windows-version\", \"windows7-compat\"]", "target": 11906320761866078153, "profile": 15657897354478470176, "path": 16179478553355585338, "deps": [[947818755262499932, "notify_rust", false, 15152759636356369927], [3150220818285335163, "url", false, 1416861958194483817], [5986029879202738730, "log", false, 14769126643489177774], [7760050409050412348, "build_script_build", false, 16453549644116911183], [9689903380558560274, "serde", false, 15388555481042839483], [10806645703491011684, "thiserror", false, 14050447952190329466], [12409575957772518135, "time", false, 9400177914969304850], [12986574360607194341, "serde_repr", false, 3493978946699085433], [13208667028893622512, "rand", false, 13811273187851468906], [14039947826026167952, "tauri", false, 3537836865984420754], [15367738274754116744, "serde_json", false, 6710690727211752251]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-notification-4467aecfb32007e4\\dep-lib-tauri_plugin_notification", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}