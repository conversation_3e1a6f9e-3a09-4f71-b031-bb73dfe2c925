{"rustc": 16591470773350601817, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 15657897354478470176, "path": 8933391823735123226, "deps": [[40386456601120721, "percent_encoding", false, 1032896724683142935], [1200537532907108615, "url<PERSON><PERSON>n", false, 7407032099364465232], [2013030631243296465, "webview2_com", false, 3847180084315592198], [2671782512663819132, "tauri_utils", false, 12528380575152632993], [3150220818285335163, "url", false, 1416861958194483817], [3331586631144870129, "getrandom", false, 18279136815259493055], [4143744114649553716, "raw_window_handle", false, 17105436284664801499], [4494683389616423722, "muda", false, 3387299462811199415], [4919829919303820331, "serialize_to_javascript", false, 18101062131316816352], [5986029879202738730, "log", false, 14769126643489177774], [6089812615193535349, "tauri_runtime", false, 7456916033740050946], [7573826311589115053, "tauri_macros", false, 1252285530422973600], [9010263965687315507, "http", false, 11446004077522401511], [9689903380558560274, "serde", false, 15388555481042839483], [10229185211513642314, "mime", false, 11609067068232102525], [10806645703491011684, "thiserror", false, 14050447952190329466], [11599800339996261026, "tauri_runtime_wry", false, 14670095805799714580], [11989259058781683633, "dunce", false, 8590979808394610163], [12393800526703971956, "tokio", false, 5749071460790026160], [12565293087094287914, "window_vibrancy", false, 883368671942732591], [12986574360607194341, "serde_repr", false, 3493978946699085433], [13077543566650298139, "heck", false, 3525024826125644909], [13625485746686963219, "anyhow", false, 7358451567492720926], [14039947826026167952, "build_script_build", false, 15451729950648944262], [14585479307175734061, "windows", false, 16004233624952482235], [15367738274754116744, "serde_json", false, 6710690727211752251], [16928111194414003569, "dirs", false, 6321439017185370490], [17155886227862585100, "glob", false, 278776085209017343]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-b5b29d147fd15065\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}