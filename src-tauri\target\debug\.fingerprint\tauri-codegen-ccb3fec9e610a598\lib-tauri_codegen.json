{"rustc": 16591470773350601817, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\"]", "target": 17460618180909919773, "profile": 2225463790103693989, "path": 8389399741903691942, "deps": [[2671782512663819132, "tauri_utils", false, 3599876494198880509], [3060637413840920116, "proc_macro2", false, 2047289033467063536], [3150220818285335163, "url", false, 15143823933437598347], [4899080583175475170, "semver", false, 14021611148097131304], [4974441333307933176, "syn", false, 1189136657649781392], [7170110829644101142, "json_patch", false, 8793476942472964823], [7392050791754369441, "ico", false, 8964234424307575842], [8319709847752024821, "uuid", false, 10767851155641868518], [9556762810601084293, "brotli", false, 16489147684619443], [9689903380558560274, "serde", false, 4366531233704107044], [9857275760291862238, "sha2", false, 3962410139016422033], [10806645703491011684, "thiserror", false, 14050447952190329466], [12687914511023397207, "png", false, 15626795865982038094], [13077212702700853852, "base64", false, 14250978220670449080], [15367738274754116744, "serde_json", false, 9981759898324134207], [15622660310229662834, "walkdir", false, 2605465889613629597], [17990358020177143287, "quote", false, 13298145250187227537]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-ccb3fec9e610a598\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}